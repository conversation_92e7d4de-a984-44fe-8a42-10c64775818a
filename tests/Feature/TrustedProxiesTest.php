<?php

namespace Tests\Feature;

use App\Models\Domain;
use App\Models\Link;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class TrustedProxiesTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test domain
        $this->domain = Domain::factory()->create([
            'host' => 'example.com',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);
    }

    /** @test */
    public function it_detects_real_ip_from_x_forwarded_for_header()
    {
        // Create a request with X-Forwarded-For header (simulating proxy)
        $request = Request::create('https://example.com/test', 'GET', [], [], [], [
            'HTTP_X_FORWARDED_FOR' => '*************',
            'REMOTE_ADDR' => '127.0.0.1', // This would be the proxy IP
        ]);

        // Set the request in the application
        $this->app->instance('request', $request);

        // Test that the IP is correctly detected
        $this->assertEquals('*************', $request->ip());
    }

    /** @test */
    public function it_detects_real_ip_from_multiple_x_forwarded_for_ips()
    {
        // Create a request with multiple IPs in X-Forwarded-For header
        $request = Request::create('https://example.com/test', 'GET', [], [], [], [
            'HTTP_X_FORWARDED_FOR' => '*************, ********, **********',
            'REMOTE_ADDR' => '127.0.0.1',
        ]);

        // Set the request in the application
        $this->app->instance('request', $request);

        // Test that the first (original client) IP is correctly detected
        $this->assertEquals('*************', $request->ip());
    }

    /** @test */
    public function it_falls_back_to_remote_addr_when_no_proxy_headers()
    {
        // Create a request without proxy headers
        $request = Request::create('https://example.com/test', 'GET', [], [], [], [
            'REMOTE_ADDR' => '***********',
        ]);

        // Set the request in the application
        $this->app->instance('request', $request);

        // Test that it falls back to REMOTE_ADDR
        $this->assertEquals('***********', $request->ip());
    }

    /** @test */
    public function it_handles_x_real_ip_header()
    {
        // Create a request with X-Real-IP header (common with nginx)
        $request = Request::create('https://example.com/test', 'GET', [], [], [], [
            'HTTP_X_REAL_IP' => '************',
            'REMOTE_ADDR' => '127.0.0.1',
        ]);

        // Set the request in the application
        $this->app->instance('request', $request);

        // Test that the real IP is detected
        $this->assertEquals('************', $request->ip());
    }

    /** @test */
    public function link_visit_service_captures_correct_ip_with_proxy_headers()
    {
        // Create a link
        $link = Link::factory()->create([
            'original_url' => 'https://example.org',
            'slug' => 'test123',
        ]);
        $link->domains()->attach($this->domain);

        // Make a request with proxy headers
        $response = $this->withHeaders([
            'X-Forwarded-For' => '************',
            'Host' => 'example.com',
        ])->get('https://example.com/test123');

        // The response should be a redirect
        $response->assertRedirect('https://example.org');

        // Check that the job was dispatched with the correct IP
        // Note: In a real test, you might want to use Queue::fake() and assert the job was pushed
        // For now, we're just verifying the redirect works
    }
}
