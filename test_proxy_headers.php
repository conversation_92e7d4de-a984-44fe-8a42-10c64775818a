<?php

// Simple test script to verify proxy header handling
// This simulates what happens when requests come through Docker

echo "=== Testing Proxy Header Detection ===\n\n";

// Test 1: X-Forwarded-For header (most common)
echo "Test 1: X-Forwarded-For header\n";
$_SERVER['HTTP_X_FORWARDED_FOR'] = '*************';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';

function getClientIP() {
    // Check for X-Forwarded-For header (most common with proxies/load balancers)
    if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ips[0]); // First IP is the original client
    }
    
    // Check for X-Real-IP header (common with nginx)
    if (!empty($_SERVER['HTTP_X_REAL_IP'])) {
        return $_SERVER['HTTP_X_REAL_IP'];
    }
    
    // Check for X-Forwarded header
    if (!empty($_SERVER['HTTP_X_FORWARDED'])) {
        return $_SERVER['HTTP_X_FORWARDED'];
    }
    
    // Check for Forwarded header (RFC 7239)
    if (!empty($_SERVER['HTTP_FORWARDED'])) {
        if (preg_match('/for=([^;,\s]+)/', $_SERVER['HTTP_FORWARDED'], $matches)) {
            return trim($matches[1], '"');
        }
    }
    
    // Fall back to REMOTE_ADDR
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

echo "X-Forwarded-For: " . $_SERVER['HTTP_X_FORWARDED_FOR'] . "\n";
echo "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'] . "\n";
echo "Detected IP: " . getClientIP() . "\n\n";

// Test 2: Multiple IPs in X-Forwarded-For
echo "Test 2: Multiple IPs in X-Forwarded-For\n";
$_SERVER['HTTP_X_FORWARDED_FOR'] = '************, ********, **********';
echo "X-Forwarded-For: " . $_SERVER['HTTP_X_FORWARDED_FOR'] . "\n";
echo "Detected IP: " . getClientIP() . "\n\n";

// Test 3: X-Real-IP header
echo "Test 3: X-Real-IP header\n";
unset($_SERVER['HTTP_X_FORWARDED_FOR']);
$_SERVER['HTTP_X_REAL_IP'] = '************';
echo "X-Real-IP: " . $_SERVER['HTTP_X_REAL_IP'] . "\n";
echo "Detected IP: " . getClientIP() . "\n\n";

// Test 4: No proxy headers (direct connection)
echo "Test 4: No proxy headers\n";
unset($_SERVER['HTTP_X_REAL_IP']);
$_SERVER['REMOTE_ADDR'] = '*************';
echo "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'] . "\n";
echo "Detected IP: " . getClientIP() . "\n\n";

echo "=== Docker Compose Simulation ===\n";
echo "When using Docker Compose without proper proxy configuration:\n";
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
unset($_SERVER['HTTP_X_FORWARDED_FOR']);
unset($_SERVER['HTTP_X_REAL_IP']);
echo "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'] . "\n";
echo "Detected IP: " . getClientIP() . " (This is the problem!)\n\n";

echo "When using Docker Compose WITH proxy headers:\n";
$_SERVER['HTTP_X_FORWARDED_FOR'] = '*************';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
echo "X-Forwarded-For: " . $_SERVER['HTTP_X_FORWARDED_FOR'] . "\n";
echo "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'] . "\n";
echo "Detected IP: " . getClientIP() . " (This is correct!)\n";
